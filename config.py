"""
Конфигурационный файл для Telegram фермы
Содержит настройки групп, лимиты действий и списки реакций
"""

import os
from typing import List, Dict, Any

# =============================================================================
# ОСНОВНЫЕ НАСТРОЙКИ
# =============================================================================

# Путь к файлу с аккаунтами
ACCOUNTS_FILE = "accounts.csv"

# Папка для хранения сессий
SESSIONS_DIR = "sessions"

# Создаем папку для сессий если её нет
os.makedirs(SESSIONS_DIR, exist_ok=True)

# =============================================================================
# ЦЕЛЕВЫЕ ГРУППЫ
# =============================================================================

# Список целевых групп для активности (username или ID)
# ВАЖНО: Замените на реальные группы перед использованием
TARGET_GROUPS = [
    "@example_group1",
    "@example_group2", 
    "@example_group3",
    # Добавьте свои группы здесь
]

# =============================================================================
# ЛИМИТЫ ДЕЙСТВИЙ (в сутки на один аккаунт)
# =============================================================================

DAILY_LIMITS = {
    "comments": 5,      # Максимум комментариев в день
    "reactions": 20,    # Максимум реакций в день
    "dialogues": 3,     # Максимум диалогов в день
}

# =============================================================================
# ВРЕМЕННЫЕ ИНТЕРВАЛЫ (в секундах)
# =============================================================================

# Задержки между действиями одного аккаунта
ACTION_DELAYS = {
    "min_delay": 15 * 60,   # Минимум 15 минут
    "max_delay": 60 * 60,   # Максимум 60 минут
}

# Задержки для имитации набора текста
TYPING_DELAYS = {
    "min_typing": 3,        # Минимум 3 секунды
    "max_typing": 8,        # Максимум 8 секунд
}

# Задержки между ответами в диалогах
DIALOGUE_DELAYS = {
    "min_reply_delay": 5 * 60,    # Минимум 5 минут
    "max_reply_delay": 20 * 60,   # Максимум 20 минут
}

# =============================================================================
# РЕАКЦИИ
# =============================================================================

# Список доступных реакций для постов
AVAILABLE_REACTIONS = [
    "👍", "🔥", "❤️", "👏", "😍", "🎉", "💯", "⚡", "🚀", "👌"
]

# =============================================================================
# SPINTAX ШАБЛОНЫ ДЛЯ КОММЕНТАРИЕВ
# =============================================================================

# Шаблоны для обычных комментариев
COMMENT_TEMPLATES = [
    "{Привет|Добрый день|Здравствуйте}! {Отличный|Хороший|Классный} пост, {спасибо|благодарю}!",
    "{Интересно|Познавательно|Полезно}! {Спасибо за информацию|Благодарю за пост}!",
    "{Согласен|Поддерживаю|Полностью согласен}! {Очень актуально|Важная тема}.",
    "{Круто|Здорово|Отлично}! {Жду продолжения|Интересно узнать больше}.",
    "{Спасибо|Благодарю} за {пост|информацию|материал}! {Очень полезно|Пригодится}.",
]

# Шаблоны для ответов в диалогах
REPLY_TEMPLATES = [
    "{Согласен|Поддерживаю|Да, точно}! {Хорошая мысль|Правильно говоришь}.",
    "{Интересная точка зрения|Любопытно}! {Не думал об этом|Стоит подумать}.",
    "{Спасибо за ответ|Благодарю}! {Полезно знать|Хорошо объяснил}.",
    "{Да|Верно|Точно}, {я тоже так думаю|согласен с тобой}.",
    "{Хороший вопрос|Интересно}! {Тоже интересует|Жду ответа}.",
]

# =============================================================================
# НАСТРОЙКИ ЛОГИРОВАНИЯ
# =============================================================================

LOG_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "date_format": "%Y-%m-%d %H:%M:%S"
}

# =============================================================================
# НАСТРОЙКИ ПРОКСИ
# =============================================================================

PROXY_CONFIG = {
    "proxy_type": "socks5",  # Тип прокси
    "timeout": 30,           # Таймаут подключения
}

# =============================================================================
# НАСТРОЙКИ TELEGRAM API
# =============================================================================

TELEGRAM_CONFIG = {
    "device_model": "Desktop",
    "system_version": "Windows 10",
    "app_version": "4.8.4",
    "lang_code": "ru",
    "system_lang_code": "ru-RU",
}

# =============================================================================
# НАСТРОЙКИ БЕЗОПАСНОСТИ
# =============================================================================

SECURITY_CONFIG = {
    "max_flood_wait": 3600,     # Максимальное время ожидания при FloodWait (1 час)
    "flood_wait_multiplier": 1.5, # Множитель для дополнительного ожидания
    "max_retries": 3,           # Максимальное количество попыток
    "retry_delay": 60,          # Задержка между попытками (секунды)
}
