"""
Модуль для ведения диалогов между аккаунтами фермы
Имитирует естественное общение в комментариях
"""

import asyncio
import random
import logging
from typing import Optional, List, Dict, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass

from telethon import TelegramClient
from telethon.errors import FloodWaitError, ChatWriteForbiddenError
from telethon.tl.types import Message

from spintax import generate_reply
from config import (
    REPLY_TEMPLATES, 
    DIALOGUE_DELAYS, 
    DAILY_LIMITS,
    TYPING_DELAYS
)

# Настройка логирования
logger = logging.getLogger(__name__)


@dataclass
class PendingReply:
    """Структура для хранения информации о запланированном ответе"""
    original_comment_id: int
    original_phone: str
    group: str
    reply_time: datetime
    replier_phone: str
    chain_length: int = 1  # Длина цепочки диалога


class DialogueManager:
    """Управляет диалогами между аккаунтами"""
    
    def __init__(self):
        self.pending_replies: List[PendingReply] = []
        self.dialogue_counts: Dict[str, int] = {}  # Счетчик диалогов по дням
        self.last_reset: datetime = datetime.now()
        self.active_dialogues: Dict[int, List[str]] = {}  # message_id -> [phone1, phone2, ...]
    
    def _reset_daily_counts(self):
        """Сбрасывает дневные счетчики если наступил новый день"""
        now = datetime.now()
        if now.date() > self.last_reset.date():
            self.dialogue_counts.clear()
            self.last_reset = now
    
    def can_start_dialogue(self, phone: str) -> bool:
        """Проверяет можно ли начать новый диалог"""
        self._reset_daily_counts()
        
        current_count = self.dialogue_counts.get(phone, 0)
        return current_count < DAILY_LIMITS["dialogues"]
    
    def add_dialogue(self, phone: str):
        """Увеличивает счетчик диалогов"""
        self._reset_daily_counts()
        self.dialogue_counts[phone] = self.dialogue_counts.get(phone, 0) + 1
    
    def schedule_reply(
        self, 
        comment_info: Dict[str, Any], 
        available_phones: List[str]
    ) -> Optional[PendingReply]:
        """
        Планирует ответ на комментарий
        
        Args:
            comment_info: Информация о комментарии
            available_phones: Список доступных аккаунтов для ответа
            
        Returns:
            Optional[PendingReply]: Запланированный ответ или None
        """
        original_phone = comment_info["phone"]
        
        # Исключаем автора оригинального комментария
        potential_repliers = [p for p in available_phones if p != original_phone]
        
        if not potential_repliers:
            logger.warning("Нет доступных аккаунтов для ответа")
            return None
        
        # Выбираем случайного отвечающего
        replier_phone = random.choice(potential_repliers)
        
        # Проверяем лимиты
        if not self.can_start_dialogue(replier_phone):
            logger.info(f"[{replier_phone}] Достигнут лимит диалогов")
            return None
        
        # Рассчитываем время ответа
        delay_seconds = random.randint(
            DIALOGUE_DELAYS["min_reply_delay"],
            DIALOGUE_DELAYS["max_reply_delay"]
        )
        reply_time = datetime.now() + timedelta(seconds=delay_seconds)
        
        pending_reply = PendingReply(
            original_comment_id=comment_info["comment_id"],
            original_phone=original_phone,
            group=comment_info["group"],
            reply_time=reply_time,
            replier_phone=replier_phone
        )
        
        self.pending_replies.append(pending_reply)
        
        logger.info(
            f"Запланирован ответ от [{replier_phone}] на комментарий [{original_phone}] "
            f"в {reply_time.strftime('%H:%M:%S')}"
        )
        
        return pending_reply
    
    def get_ready_replies(self) -> List[PendingReply]:
        """Возвращает ответы готовые к отправке"""
        now = datetime.now()
        ready_replies = [r for r in self.pending_replies if r.reply_time <= now]
        
        # Удаляем готовые ответы из списка ожидания
        self.pending_replies = [r for r in self.pending_replies if r.reply_time > now]
        
        return ready_replies
    
    def should_continue_dialogue(self, message_id: int, max_chain_length: int = 3) -> bool:
        """
        Определяет стоит ли продолжить диалог
        
        Args:
            message_id: ID сообщения
            max_chain_length: Максимальная длина цепочки
            
        Returns:
            bool: True если стоит продолжить
        """
        if message_id not in self.active_dialogues:
            return False
        
        chain_length = len(self.active_dialogues[message_id])
        
        # Случайность продолжения диалога (чем длиннее цепочка, тем меньше вероятность)
        continue_probability = max(0.1, 0.8 - (chain_length * 0.2))
        
        return (
            chain_length < max_chain_length and 
            random.random() < continue_probability
        )
    
    def add_to_dialogue_chain(self, original_message_id: int, phone: str):
        """Добавляет участника в цепочку диалога"""
        if original_message_id not in self.active_dialogues:
            self.active_dialogues[original_message_id] = []
        
        self.active_dialogues[original_message_id].append(phone)


# Глобальный менеджер диалогов
dialogue_manager = DialogueManager()


async def simulate_typing_for_reply(client: TelegramClient, chat, text: str):
    """Имитирует набор текста для ответа"""
    base_time = random.uniform(TYPING_DELAYS["min_typing"], TYPING_DELAYS["max_typing"])
    text_length_factor = len(text) * 0.04  # Немного быстрее для ответов
    
    typing_time = min(base_time + text_length_factor, 12)  # Максимум 12 секунд
    
    try:
        async with client.action(chat, 'typing'):
            await asyncio.sleep(typing_time)
    except Exception as e:
        logger.warning(f"Не удалось отправить действие typing: {e}")


async def send_reply(
    client: TelegramClient,
    phone: str,
    group: str,
    original_message_id: int,
    custom_text: Optional[str] = None
) -> Optional[Message]:
    """
    Отправляет ответ на сообщение
    
    Args:
        client: Telegram клиент
        phone: Номер телефона отвечающего
        group: Группа
        original_message_id: ID оригинального сообщения
        custom_text: Пользовательский текст ответа
        
    Returns:
        Optional[Message]: Отправленное сообщение или None
    """
    try:
        # Генерируем текст ответа
        if custom_text:
            reply_text = custom_text
        else:
            reply_text = generate_reply(REPLY_TEMPLATES)
        
        logger.info(f"[{phone}] Готовлю ответ в {group}: '{reply_text}'")
        
        # Имитируем набор текста
        await simulate_typing_for_reply(client, group, reply_text)
        
        # Отправляем ответ
        sent_message = await client.send_message(
            group,
            reply_text,
            reply_to=original_message_id,
            parse_mode='html'
        )
        
        logger.info(f"[{phone}] Ответ отправлен в {group} (ID: {sent_message.id})")
        return sent_message
        
    except FloodWaitError as e:
        wait_time = e.seconds + random.randint(60, 300)
        logger.warning(f"[{phone}] FloodWait {e.seconds}с, ждем {wait_time}с")
        await asyncio.sleep(wait_time)
        return None
        
    except (ChatWriteForbiddenError, Exception) as e:
        logger.error(f"[{phone}] Ошибка при отправке ответа в {group}: {e}")
        return None


async def process_pending_reply(
    clients: Dict[str, TelegramClient],
    pending_reply: PendingReply
) -> Optional[Dict[str, Any]]:
    """
    Обрабатывает запланированный ответ
    
    Args:
        clients: Словарь клиентов {phone: client}
        pending_reply: Информация о запланированном ответе
        
    Returns:
        Optional[Dict]: Информация об отправленном ответе
    """
    replier_phone = pending_reply.replier_phone
    
    if replier_phone not in clients:
        logger.error(f"Клиент {replier_phone} не найден")
        return None
    
    client = clients[replier_phone]
    
    # Отправляем ответ
    sent_message = await send_reply(
        client,
        replier_phone,
        pending_reply.group,
        pending_reply.original_comment_id
    )
    
    if sent_message:
        # Обновляем счетчики
        dialogue_manager.add_dialogue(replier_phone)
        dialogue_manager.add_to_dialogue_chain(
            pending_reply.original_comment_id, 
            replier_phone
        )
        
        return {
            "replier_phone": replier_phone,
            "original_phone": pending_reply.original_phone,
            "group": pending_reply.group,
            "original_comment_id": pending_reply.original_comment_id,
            "reply_id": sent_message.id,
            "reply_text": sent_message.text,
            "timestamp": datetime.now()
        }
    
    return None


def get_dialogue_stats(phone: str) -> Dict[str, int]:
    """Возвращает статистику диалогов для аккаунта"""
    dialogue_manager._reset_daily_counts()
    return {
        "dialogues_today": dialogue_manager.dialogue_counts.get(phone, 0),
        "pending_replies": len([
            r for r in dialogue_manager.pending_replies 
            if r.replier_phone == phone
        ])
    }
