"""
Модуль комментатора для Telegram фермы
Отвечает за оставление комментариев под постами с имитацией естественного поведения
"""

import asyncio
import random
import logging
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta

from telethon import TelegramClient
from telethon.errors import FloodWaitError, ChatWriteForbiddenError, UserBannedInChannelError
from telethon.tl.types import Message

from spintax import generate_comment
from config import (
    COMMENT_TEMPLATES, 
    TYPING_DELAYS, 
    DAILY_LIMITS,
    SECURITY_CONFIG
)

# Настройка логирования
logger = logging.getLogger(__name__)


class CommentTracker:
    """Отслеживает количество комментариев для каждого аккаунта"""
    
    def __init__(self):
        self.daily_counts: Dict[str, Dict[str, int]] = {}
        self.last_reset: Dict[str, datetime] = {}
    
    def _reset_if_new_day(self, phone: str):
        """Сбрасывает счетчики если наступил новый день"""
        now = datetime.now()
        if phone not in self.last_reset:
            self.last_reset[phone] = now
            self.daily_counts[phone] = {"comments": 0}
            return
        
        if now.date() > self.last_reset[phone].date():
            self.daily_counts[phone] = {"comments": 0}
            self.last_reset[phone] = now
    
    def can_comment(self, phone: str) -> bool:
        """Проверяет можно ли оставить комментарий"""
        self._reset_if_new_day(phone)
        
        if phone not in self.daily_counts:
            self.daily_counts[phone] = {"comments": 0}
        
        return self.daily_counts[phone]["comments"] < DAILY_LIMITS["comments"]
    
    def add_comment(self, phone: str):
        """Увеличивает счетчик комментариев"""
        self._reset_if_new_day(phone)
        
        if phone not in self.daily_counts:
            self.daily_counts[phone] = {"comments": 0}
        
        self.daily_counts[phone]["comments"] += 1
    
    def get_stats(self, phone: str) -> Dict[str, int]:
        """Возвращает статистику для аккаунта"""
        self._reset_if_new_day(phone)
        
        if phone not in self.daily_counts:
            return {"comments": 0}
        
        return self.daily_counts[phone].copy()


# Глобальный трекер комментариев
comment_tracker = CommentTracker()


async def simulate_typing(client: TelegramClient, chat, text: str):
    """
    Имитирует набор текста человеком
    
    Args:
        client: Telegram клиент
        chat: Чат для отправки действия
        text: Текст сообщения (для расчета времени набора)
    """
    # Базовое время набора
    base_time = random.uniform(TYPING_DELAYS["min_typing"], TYPING_DELAYS["max_typing"])
    
    # Дополнительное время в зависимости от длины текста
    text_length_factor = len(text) * 0.05  # 0.05 секунды на символ
    
    typing_time = min(base_time + text_length_factor, 15)  # Максимум 15 секунд
    
    try:
        async with client.action(chat, 'typing'):
            await asyncio.sleep(typing_time)
    except Exception as e:
        logger.warning(f"Не удалось отправить действие typing: {e}")


async def get_recent_posts(client: TelegramClient, group: str, limit: int = 10) -> List[Message]:
    """
    Получает последние посты из группы
    
    Args:
        client: Telegram клиент
        group: Имя группы или ID
        limit: Количество постов для получения
        
    Returns:
        List[Message]: Список сообщений
    """
    try:
        messages = []
        async for message in client.iter_messages(group, limit=limit):
            # Берем только сообщения с текстом или медиа (не служебные)
            if message.text or message.media:
                messages.append(message)
        
        return messages
    
    except Exception as e:
        logger.error(f"Ошибка при получении постов из {group}: {e}")
        return []


async def leave_comment(
    client: TelegramClient, 
    phone: str, 
    group: str, 
    message: Message,
    custom_text: Optional[str] = None
) -> Optional[Message]:
    """
    Оставляет комментарий под постом
    
    Args:
        client: Telegram клиент
        phone: Номер телефона аккаунта
        group: Группа
        message: Сообщение для комментирования
        custom_text: Пользовательский текст комментария
        
    Returns:
        Optional[Message]: Отправленное сообщение или None при ошибке
    """
    try:
        # Проверяем лимиты
        if not comment_tracker.can_comment(phone):
            logger.info(f"[{phone}] Достигнут дневной лимит комментариев")
            return None
        
        # Генерируем текст комментария
        if custom_text:
            comment_text = custom_text
        else:
            comment_text = generate_comment(COMMENT_TEMPLATES)
        
        logger.info(f"[{phone}] Готовлю комментарий в {group}: '{comment_text}'")
        
        # Имитируем набор текста
        await simulate_typing(client, group, comment_text)
        
        # Отправляем комментарий
        sent_message = await client.send_message(
            group, 
            comment_text, 
            reply_to=message.id,
            parse_mode='html'
        )
        
        # Обновляем счетчик
        comment_tracker.add_comment(phone)
        
        logger.info(f"[{phone}] Комментарий отправлен в {group} (ID: {sent_message.id})")
        return sent_message
        
    except FloodWaitError as e:
        wait_time = e.seconds + random.randint(60, 300)  # Дополнительная задержка
        logger.warning(f"[{phone}] FloodWait {e.seconds}с, ждем {wait_time}с")
        await asyncio.sleep(wait_time)
        return None
        
    except (ChatWriteForbiddenError, UserBannedInChannelError) as e:
        logger.error(f"[{phone}] Нет прав для комментирования в {group}: {e}")
        return None
        
    except Exception as e:
        logger.error(f"[{phone}] Ошибка при отправке комментария в {group}: {e}")
        return None


async def comment_random_post(
    client: TelegramClient, 
    phone: str, 
    groups: List[str]
) -> Optional[Dict[str, Any]]:
    """
    Комментирует случайный пост в случайной группе
    
    Args:
        client: Telegram клиент
        phone: Номер телефона аккаунта
        groups: Список групп для комментирования
        
    Returns:
        Optional[Dict]: Информация о комментарии или None
    """
    if not groups:
        logger.warning(f"[{phone}] Список групп пуст")
        return None
    
    # Проверяем лимиты
    if not comment_tracker.can_comment(phone):
        logger.info(f"[{phone}] Достигнут дневной лимит комментариев")
        return None
    
    # Выбираем случайную группу
    group = random.choice(groups)
    
    try:
        # Получаем последние посты
        posts = await get_recent_posts(client, group, limit=10)
        
        if not posts:
            logger.warning(f"[{phone}] Нет доступных постов в {group}")
            return None
        
        # Выбираем случайный пост
        post = random.choice(posts)
        
        # Оставляем комментарий
        sent_message = await leave_comment(client, phone, group, post)
        
        if sent_message:
            return {
                "phone": phone,
                "group": group,
                "post_id": post.id,
                "comment_id": sent_message.id,
                "comment_text": sent_message.text,
                "timestamp": datetime.now()
            }
        
        return None
        
    except Exception as e:
        logger.error(f"[{phone}] Ошибка при комментировании в {group}: {e}")
        return None


def get_comment_stats(phone: str) -> Dict[str, int]:
    """
    Возвращает статистику комментариев для аккаунта
    
    Args:
        phone: Номер телефона аккаунта
        
    Returns:
        Dict[str, int]: Статистика комментариев
    """
    return comment_tracker.get_stats(phone)
