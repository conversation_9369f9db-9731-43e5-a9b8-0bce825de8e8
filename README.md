# Telegram Ферма - Автоматизация активности аккаунтов

Модульная система для управления фермой Telegram-аккаунтов с имитацией естественного поведения человека.

## 🎯 Возможности

- **Комментирование постов** с использованием Spintax-шаблонов
- **Ведение диалогов** между аккаунтами фермы
- **Проставление реакций** на посты с умным выбором эмодзи
- **Имитация естественного поведения** (задержки, typing action)
- **Система лимитов** для предотвращения блокировок
- **Работа через прокси** (SOCKS5)
- **Обработка ошибок** и FloodWait

## 📁 Структура проекта

```
telegram_farm/
├── main.py              # Главный оркестратор
├── config.py            # Конфигурация
├── spintax.py           # Обработка Spintax-текстов
├── accounts.csv         # Данные аккаунтов
├── requirements.txt     # Зависимости
├── sessions/            # Папка для сессий
└── modules/
    ├── commenter.py     # Модуль комментирования
    ├── dialogue.py      # Модуль диалогов
    └── reactor.py       # Модуль реакций
```

## 🚀 Быстрый старт

### 1. Установка зависимостей

```bash
pip install -r requirements.txt
```

### 2. Настройка аккаунтов

Отредактируйте файл `accounts.csv`:

```csv
phone_number,api_id,api_hash,proxy_address,proxy_port,proxy_user,proxy_pass
+***********,********,your_api_hash,proxy1.com,1080,user1,pass1
+***********,********,your_api_hash,proxy2.com,1080,user2,pass2
```

**Где взять API данные:**
1. Перейдите на https://my.telegram.org
2. Войдите с номером телефона
3. Создайте приложение в разделе "API development tools"
4. Скопируйте `api_id` и `api_hash`

### 3. Настройка целевых групп

В файле `config.py` укажите группы для активности:

```python
TARGET_GROUPS = [
    "@your_target_group1",
    "@your_target_group2",
    # Добавьте свои группы
]
```

### 4. Запуск

```bash
python main.py
```

При первом запуске потребуется ввести коды авторизации для каждого аккаунта.

## ⚙️ Конфигурация

### Лимиты действий (в сутки на аккаунт)

```python
DAILY_LIMITS = {
    "comments": 5,      # Комментарии
    "reactions": 20,    # Реакции
    "dialogues": 3,     # Диалоги
}
```

### Временные интервалы

```python
ACTION_DELAYS = {
    "min_delay": 15 * 60,   # Минимум между действиями (15 мин)
    "max_delay": 60 * 60,   # Максимум между действиями (60 мин)
}
```

### Шаблоны комментариев (Spintax)

```python
COMMENT_TEMPLATES = [
    "{Привет|Добрый день}! {Отличный|Хороший} пост, {спасибо|благодарю}!",
    "{Интересно|Познавательно}! {Спасибо за информацию|Благодарю}!",
]
```

## 🔧 Модули

### Комментатор (`modules/commenter.py`)

- Выбирает случайную группу и пост
- Генерирует комментарий из Spintax-шаблонов
- Имитирует набор текста
- Отслеживает лимиты

### Диалоги (`modules/dialogue.py`)

- Планирует ответы на комментарии
- Создает цепочки диалогов между аккаунтами
- Контролирует длину диалогов

### Реакции (`modules/reactor.py`)

- Умный выбор реакций по ключевым словам
- Предотвращение спама реакций
- Работа только с свежими постами

### Spintax (`spintax.py`)

- Обработка шаблонов `{вариант1|вариант2|вариант3}`
- Генерация уникальных текстов
- Валидация шаблонов

## 🛡️ Безопасность

### Имитация человеческого поведения

- Случайные задержки между действиями
- Имитация набора текста с typing action
- Вариативность текстов через Spintax
- Ограничения по частоте действий

### Обработка ошибок

- Автоматическое ожидание при FloodWait
- Пропуск недоступных групп
- Логирование всех действий
- Graceful shutdown

### Прокси

- Обязательное использование уникальных SOCKS5 прокси
- Автоматическая настройка для каждого аккаунта

## 📊 Мониторинг

Скрипт выводит статистику каждые 10 циклов:

```
СТАТИСТИКА ФЕРМЫ:
Комментарии: 15
Реакции: 45
Диалоги: 8
Ошибки: 2

[+***********] Комментарии: 3, Реакции: 12, Диалоги: 2
[+***********] Комментарии: 2, Реакции: 8, Диалоги: 1
```

## ⚠️ Важные замечания

1. **Используйте только свои аккаунты** или аккаунты с разрешением владельца
2. **Обязательно используйте прокси** для каждого аккаунта
3. **Не превышайте лимиты** - это может привести к блокировкам
4. **Тестируйте на небольших группах** перед масштабированием
5. **Регулярно проверяйте логи** на наличие ошибок

## 🔄 Обновления

Для обновления зависимостей:

```bash
pip install -r requirements.txt --upgrade
```

## 📝 Логи

Все действия логируются в консоль с указанием:
- Времени действия
- Номера аккаунта
- Типа действия
- Результата

## 🆘 Решение проблем

### "Файл accounts.csv не найден"
Создайте файл с данными аккаунтов по образцу

### "Не указаны целевые группы"
Добавьте группы в `TARGET_GROUPS` в config.py

### "FloodWait ошибки"
Увеличьте задержки в `ACTION_DELAYS`

### "Ошибки авторизации"
Проверьте правильность api_id, api_hash и прокси

---

**⚡ Создано для образовательных целей. Используйте ответственно!**
